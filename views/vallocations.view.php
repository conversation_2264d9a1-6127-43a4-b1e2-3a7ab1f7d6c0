<?php
#region DOCS
/** @var float $selected_valor */
/** @var App\classes\Allocation[] $allocations */
/** @var array<string, App\classes\AllocationItem[]> $itemsByAllocation */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Desglose de Allocations</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <div class="d-flex align-items-center justify-content-between">
            <h1 class="page-header mb-0">Desglose de Allocations</h1>
            <div class="d-flex align-items-center">
                <button type="button" id="btnResetAgregar" class="btn btn-warning me-2">
                    <i class="fa fa-undo me-2"></i>Resetear Agregar
                </button>
                <a href="gtransacciones" class="btn btn-default">Regresar</a>
            </div>
        </div>
        <hr>
        <!-- END page-header -->

        <!-- Header summary -->
        <div class="row mb-3">
            <div class="col-md-12">
                <div class="alert alert-secondary mb-0">
                    <div>
                        <span class="badge bg-secondary fs-15px">Valor seleccionado: $<?php echo format_currency($selected_valor); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (empty($allocations)): ?>
            <div class="alert alert-warning">No hay allocations activos.</div>
        <?php else: ?>
            <?php foreach ($allocations as $allocation): ?>
                <?php
                    $aNombre     = $allocation->getNombre();
                    $aPct        = (float)($allocation->getPorcentaje() ?? 0);
                    $aAmount     = (float)$selected_valor * ($aPct / 100.0);
                    $aId         = $allocation->getId();
                    $childItems  = $itemsByAllocation[$aId] ?? [];
                ?>
                <div class="panel panel-inverse mt-3">
                    <div class="panel-heading">
                        <h4 class="panel-title d-flex justify-content-between align-items-center">
                            <span><?php echo htmlspecialchars($aNombre); ?></span>
                            <span>
                                <span class="badge bg-primary ms-2 fs-12px"><?php echo number_format($aPct, 2); ?>%</span>
                                <span class="badge bg-success ms-2 fs-12px">$<?php echo format_currency($aAmount); ?></span>
                            </span>
                        </h4>
                    </div>
                    <div class="p-1 table-nowrap" style="overflow:auto">
                        <table class="table table-sm mb-0 table-hover" id="allocation-table-<?php echo $aId; ?>">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th class="text-center w-100px">%</th>
                                    <th class="text-end w-100px">Monto</th>
                                    <th class="text-end w-100px">Bolsillo</th>
                                    <th class="text-end w-100px">Agregar</th>
                                    <th class="text-end w-100px">Valor</th>
                                    <th class="text-end w-100px">Falta</th>
                                </tr>
                            </thead>
                            <tbody class="fs-13px">
                                <?php if (empty($childItems)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">Sin items</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($childItems as $item): ?>
                                        <?php
                                            $iNombre = $item->getNombre();
                                            $iPct    = (float)($item->getPorcentaje() ?? 0);
                                            $iAmount = $aAmount * ($iPct / 100.0);
                                            $iValor  = (float)($item->getValor() ?? 0);
                                            $iValorBolsillo = (float)($item->getValorBolsillo() ?? 0);
                                            $iValorBolsilloAdded = (float)($item->getValorBolsilloAdded() ?? 0);
                                            $iId     = $item->getId();

                                            // Calculate Falta: conditional calculation based on Valor
                                            if ($iValor > 0) {
                                                $iFalta = ($iValorBolsillo + $iValorBolsilloAdded) - $iValor;
                                            } else {
                                                $iFalta = 0;
                                            }
                                        ?>
                                        <tr>
                                            <td class="align-middle"><?php echo htmlspecialchars($iNombre); ?></td>
                                            <td class="align-middle text-center"><?php echo number_format($iPct, 2); ?>%</td>
                                            <td class="align-middle text-end">$<?php echo format_currency($iAmount); ?></td>
                                            <td class="align-middle text-end text-info">$<?php echo number_format($iValorBolsillo, 0, ',', '.'); ?></td>
                                            <td class="align-middle text-end editable-valor-bolsillo-added" data-id="<?php echo htmlspecialchars($iId); ?>" data-value="<?php echo $iValorBolsilloAdded; ?>" style="cursor: pointer;">
                                                <?php
                                                    if ($iValorBolsilloAdded > 0) {
                                                        echo '<span class="text-success">+$' . number_format($iValorBolsilloAdded, 0, ',', '.') . '</span>';
                                                    } elseif ($iValorBolsilloAdded < 0) {
                                                        echo '<span class="text-danger">-$' . number_format(abs($iValorBolsilloAdded), 0, ',', '.') . '</span>';
                                                    } else {
                                                        echo '$0';
                                                    }
                                                ?>
                                            </td>
                                            <td class="align-middle text-end">$<?php echo number_format($iValor, 0, ',', '.'); ?></td>
                                            <td class="align-middle text-end <?php echo ($iFalta === 0 && $iValor === 0) ? 'text-muted' : (($iFalta < 0) ? 'text-danger' : 'text-success'); ?>" data-falta-row="<?php echo htmlspecialchars($iId); ?>">
                                                $<?php echo number_format(abs($iFalta), 0, ',', '.'); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                            <tfoot class="fs-13px">
                                <tr>
                                    <td class="align-middle"></td>
                                    <td class="align-middle text-center"></td>
                                    <td class="align-middle text-end"></td>
                                    <td class="align-middle text-end"><strong>Total:</strong></td>
                                    <td class="align-middle text-end" id="total-agregar-<?php echo $aId; ?>"><strong>$0</strong></td>
                                    <td class="align-middle text-end"></td>
                                    <td class="align-middle text-end"></td>
                                </tr>
                                <tr class="fs-13px">
                                    <td class="align-middle"></td>
                                    <td class="align-middle text-center"></td>
                                    <td class="align-middle text-end"></td>
                                    <td class="align-middle text-end"><strong>Diferencia:</strong></td>
                                    <td class="align-middle text-end" id="diferencia-agregar-<?php echo $aId; ?>" data-target-amount="<?php echo $aAmount; ?>"><strong>$0</strong></td>
                                    <td class="align-middle text-end"></td>
                                    <td class="align-middle text-end"></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Save Allocations Button -->
        <?php if (!empty($allocations)): ?>
            <div class="row mt-4">
                <div class="col-md-12 text-center">
                    <button type="button" id="btnGuardarAsignaciones" class="btn btn-success btn-lg w-100">
                        <i class="fa fa-save me-2"></i>Guardar Asignaciones
                    </button>
                </div>
            </div>
        <?php endif; ?>

    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<!-- Reset Agregar Confirmation Modal -->
<div class="modal fade" id="modalConfirmResetAgregar" tabindex="-1" aria-labelledby="modalConfirmResetAgregarLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalConfirmResetAgregarLabel">Confirmar reseteo</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Cerrar"></button>
      </div>
      <div class="modal-body">
        ¿Está seguro que desea resetear todos los valores agregados (Agregar) a 0? Esta acción no se puede deshacer.
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" id="btnConfirmResetAgregar" class="btn btn-danger">
          <i class="fa fa-exclamation-triangle me-2"></i>Sí, resetear
        </button>
      </div>
    </div>
  </div>
</div>

<?php require_once __ROOT__ . '/views/js_core.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to parse Colombian peso formatted string to numeric value
    function parseCurrencyValue(currencyText) {
        if (!currencyText) return 0;
        // Remove currency symbols, spaces, and convert to number
        // Format examples: "$3.500", "$1.234.567"
        const cleanText = currencyText.replace(/[$\s]/g, '').replace(/\./g, '');
        return parseFloat(cleanText) || 0;
    }

    // Function to format number to Colombian peso format
    function formatCurrencyValue(value) {
        if (value === null || value === undefined || value === 0) return '$0';
        return '$' + Math.round(Math.abs(value)).toString().replace(/\B(?=(\d{3})+(?!\d))/g, '.');
    }

    // Function to update Falta column for a specific row
    function updateFaltaColumn(itemId, newAgregarValue) {
        const row = document.querySelector(`[data-falta-row="${itemId}"]`);
        if (!row) return;

        // Find the row containing this cell
        const tableRow = row.closest('tr');
        if (!tableRow) return;

        // Get values from the row
        const bolsilloCell = tableRow.cells[3]; // Bolsillo column (4th column, index 3)
        const valorCell = tableRow.cells[5];    // Valor column (6th column, index 5)

        const bolsilloValue = parseCurrencyValue(bolsilloCell.textContent);
        const valorValue = parseCurrencyValue(valorCell.textContent);
        const agregarValue = parseFloat(newAgregarValue) || 0;

        // Calculate Falta: conditional calculation based on Valor
        let faltaValue;
        if (valorValue > 0) {
            faltaValue = (bolsilloValue + agregarValue) - valorValue;
        } else {
            faltaValue = 0;
        }

        // Apply color coding and update content
        const faltaCell = row;
        faltaCell.className = faltaCell.className.replace(/text-(success|danger)/g, '');

        if (faltaValue < 0) {
            faltaCell.classList.add('text-danger');
        } else {
            faltaCell.classList.add('text-success');
        }

        // Update the cell content with formatted value
        faltaCell.textContent = formatCurrencyValue(faltaValue);
    }

    // Function to calculate the total of "Agregar" values for a specific table
    function calculateTableTotal(tableId) {
        const table = document.getElementById(tableId);
        if (!table) return 0;

        const agregarCells = table.querySelectorAll('.editable-valor-bolsillo-added');
        let total = 0;

        agregarCells.forEach(function(cell) {
            const value = parseFloat(cell.getAttribute('data-value')) || 0;
            total += value;
        });

        return total;
    }

    // Function to update the footer total for a specific table
    function updateTableTotal(tableId) {
        const total = calculateTableTotal(tableId);
        const totalCell = document.getElementById('total-agregar-' + tableId.replace('allocation-table-', ''));

        if (!totalCell) return;

        // Apply color coding based on total value
        totalCell.className = totalCell.className.replace(/text-(success|danger)/g, '');

        if (total > 0) {
            totalCell.classList.add('text-success');
        } else if (total < 0) {
            totalCell.classList.add('text-danger');
        }

        // Format and display the total
        const formattedTotal = formatCurrencyValue(total);
        totalCell.innerHTML = '<strong>' + formattedTotal + '</strong>';

        // Update the difference row as well
        updateTableDiferencia(tableId);
    }

    // Function to update the difference between total "Agregar" and target amount for a specific table
    function updateTableDiferencia(tableId) {
        const totalAgregar = calculateTableTotal(tableId);
        const diferenciaCell = document.getElementById('diferencia-agregar-' + tableId.replace('allocation-table-', ''));

        if (!diferenciaCell) return;

        // Get target amount from data attribute
        const targetAmount = parseFloat(diferenciaCell.getAttribute('data-target-amount')) || 0;

        // Calculate difference: total "Agregar" - target amount
        const diferencia = totalAgregar - targetAmount;

        // Apply color coding based on difference value
        diferenciaCell.className = diferenciaCell.className.replace(/text-(success|danger)/g, '');

        if (diferencia >= 0) {
            diferenciaCell.classList.add('text-success'); // At or above target (green)
        } else {
            diferenciaCell.classList.add('text-danger');  // Below target (red)
        }

        // Format and display the difference
        const formattedDiferencia = formatCurrencyValue(diferencia);
        diferenciaCell.innerHTML = '<strong>' + formattedDiferencia + '</strong>';
    }

    // Function to update all table totals
    function updateAllTableTotals() {
        const tables = document.querySelectorAll('table[id^="allocation-table-"]');
        tables.forEach(function(table) {
            updateTableTotal(table.id);
        });
    }

    // Attach double-click inline editing for valor_bolsillo_added
    function attachInlineEditValorBolsilloAdded() {
        const editableCells = document.querySelectorAll('.editable-valor-bolsillo-added');

        editableCells.forEach(function(cell) {
            cell.addEventListener('dblclick', function() {
                if (cell.dataset.editing === '1') return;
                cell.dataset.editing = '1';

                const id = cell.getAttribute('data-id');
                const currentValue = parseFloat(cell.getAttribute('data-value')) || 0;
                const originalContent = cell.innerHTML;

                // Create input field
                const input = document.createElement('input');
                input.type = 'text';
                input.className = 'form-control form-control-sm bg-transparent text-white text-end';
                input.value = currentValue.toString();
                input.style.width = '100%';

                // Replace cell content with input
                cell.innerHTML = '';
                cell.appendChild(input);
                input.focus();
                input.select();

                let replaced = false;

                const cancelEdit = () => {
                    if (replaced) return;
                    replaced = true;
                    cell.innerHTML = originalContent;
                    cell.dataset.editing = '0';
                };

                const commitEdit = () => {
                    if (replaced) return;

                    const newValue = input.value.trim();
                    if (newValue === currentValue.toString()) {
                        cancelEdit();
                        return;
                    }

                    // Send AJAX request
                    const formData = new FormData();
                    formData.append('action', 'update_allocation_item_valor_bolsillo_added');
                    formData.append('idallocationitem', id);
                    formData.append('valor_bolsillo_added', newValue);

                    fetch('editar-allocation', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            if (replaced) return;
                            replaced = true;

                            const updatedValue = parseFloat(data.newValorBolsilloAdded) || 0;
                            cell.setAttribute('data-value', updatedValue);

                            // Format the display value (Colombian peso format: thousands separator with period)
                            let displayContent;
                            if (updatedValue > 0) {
                                const formatted = Math.round(updatedValue).toLocaleString('de-DE'); // German locale uses period as thousands separator
                                displayContent = '<span class="text-success">+$' + formatted + '</span>';
                            } else if (updatedValue < 0) {
                                const formatted = Math.round(Math.abs(updatedValue)).toLocaleString('de-DE');
                                displayContent = '<span class="text-danger">-$' + formatted + '</span>';
                            } else {
                                displayContent = '$0';
                            }

                            cell.innerHTML = displayContent;
                            cell.dataset.editing = '0';

                            // Update Falta column for this row
                            updateFaltaColumn(id, updatedValue);

                            // Update table total
                            const table = cell.closest('table');
                            if (table && table.id) {
                                updateTableTotal(table.id);
                            }

                            // Show success toast
                            if (typeof toasty !== 'undefined') {
                                toasty({
                                    type: 'success',
                                    title: 'Éxito',
                                    message: data.message,
                                    settings: {
                                        position: 'top-right',
                                        duration: 3000
                                    }
                                });
                            }
                        } else {
                            // Show error toast
                            if (typeof toasty !== 'undefined') {
                                toasty({
                                    type: 'error',
                                    title: 'Error',
                                    message: data.message || 'No se pudo actualizar',
                                    settings: {
                                        position: 'top-right',
                                        duration: 5000
                                    }
                                });
                            }
                            cancelEdit();
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        if (typeof toasty !== 'undefined') {
                            toasty({
                                type: 'error',
                                title: 'Error',
                                message: 'Error de conexión',
                                settings: {
                                    position: 'top-right',
                                    duration: 5000
                                }
                            });
                        }
                        cancelEdit();
                    });
                };

                // Event listeners
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        commitEdit();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        cancelEdit();
                    }
                });

                input.addEventListener('blur', commitEdit);
            });
        });
    }

    // Initialize inline editing
    attachInlineEditValorBolsilloAdded();

    // Initialize table totals
    updateAllTableTotals();

    // Handle "Guardar Asignaciones" button click
    const btnGuardarAsignaciones = document.getElementById('btnGuardarAsignaciones');
    if (btnGuardarAsignaciones) {
        btnGuardarAsignaciones.addEventListener('click', function() {
            // Prevent multiple clicks
            if (this.disabled) return;

            // Disable button and show loading state
            this.disabled = true;
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Guardando...';

            // Prepare AJAX request
            const formData = new FormData();
            formData.append('action', 'save_allocations');

            // Make AJAX call
            fetch('editar-allocation', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success toast
                    if (typeof toasty !== 'undefined') {
                        toasty({
                            type: 'success',
                            title: 'Éxito',
                            message: data.message,
                            settings: {
                                position: 'top-right',
                                duration: 4000
                            }
                        });
                    }

                    // Redirect to the report page after a short delay
                    setTimeout(() => {
                        window.location.href = 'revisar-allocations';
                    }, 2000);
                } else {
                    // Show error toast
                    if (typeof toasty !== 'undefined') {
                        toasty({
                            type: 'error',
                            title: 'Error',
                            message: data.message || 'No se pudieron guardar las asignaciones',
                            settings: {
                                position: 'top-right',
                                duration: 5000
                            }
                        });
                    }

                    // Re-enable button
                    this.disabled = false;
                    this.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toasty !== 'undefined') {
                    toasty({
                        type: 'error',
                        title: 'Error',
                        message: 'Error de conexión al guardar las asignaciones',
                        settings: {
                            position: 'top-right',
                            duration: 5000
                        }
                    });
                }

                // Re-enable button
                this.disabled = false;
                this.innerHTML = originalText;
            });
        });
    }
    // Handle Reset Agregar flow
    const btnResetAgregar = document.getElementById('btnResetAgregar');
    if (btnResetAgregar) {
        btnResetAgregar.addEventListener('click', function() {
            const modalEl = document.getElementById('modalConfirmResetAgregar');
            if (!modalEl) return;
            if (window.bootstrap && bootstrap.Modal) {
                const modal = new bootstrap.Modal(modalEl);
                modal.show();
            } else if (window.$ && typeof $('#modalConfirmResetAgregar').modal === 'function') {
                $('#modalConfirmResetAgregar').modal('show');
            } else {
                modalEl.style.display = 'block';
                modalEl.classList.add('show');
            }
        });
    }

    const btnConfirmResetAgregar = document.getElementById('btnConfirmResetAgregar');
    if (btnConfirmResetAgregar) {
        btnConfirmResetAgregar.addEventListener('click', function() {
            if (this.disabled) return;
            const originalHtml = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i>Reseteando...';

            const formData = new FormData();
            formData.append('action', 'reset_agregar');

            fetch('editar-allocation', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                const modalEl = document.getElementById('modalConfirmResetAgregar');
                if (data.status === 'success') {
                    // Hide modal
                    if (modalEl) {
                        if (window.bootstrap && bootstrap.Modal) {
                            const instance = bootstrap.Modal.getInstance(modalEl) || new bootstrap.Modal(modalEl);
                            instance.hide();
                        } else if (window.$ && typeof $('#modalConfirmResetAgregar').modal === 'function') {
                            $('#modalConfirmResetAgregar').modal('hide');
                        } else {
                            modalEl.classList.remove('show');
                            modalEl.style.display = 'none';
                        }
                    }

                    // Success toast
                    if (typeof toasty !== 'undefined') {
                        toasty({
                            type: 'success',
                            title: 'Éxito',
                            message: data.message || 'Valores reseteados correctamente',
                            settings: {
                                position: 'top-right',
                                duration: 3000
                            }
                        });
                    }

                    // Reload to reflect changes
                    setTimeout(() => { window.location.reload(); }, 1200);
                } else {
                    if (typeof toasty !== 'undefined') {
                        toasty({
                            type: 'error',
                            title: 'Error',
                            message: data.message || 'No se pudo resetear los valores',
                            settings: {
                                position: 'top-right',
                                duration: 5000
                            }
                        });
                    }
                    this.disabled = false;
                    this.innerHTML = originalHtml;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (typeof toasty !== 'undefined') {
                    toasty({
                        type: 'error',
                        title: 'Error',
                        message: 'Error de conexión',
                        settings: {
                            position: 'top-right',
                            duration: 5000
                        }
                    });
                }
                this.disabled = false;
                this.innerHTML = originalHtml;
            });
        });
    }

});
</script>

</body>
</html>